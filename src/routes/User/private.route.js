import {GoogleReCaptchaProvider} from "react-google-recaptcha-v3";
import {
  Home,
  HomeElectron,
  ElectronLogin,
  CompleteElectronLogin,
  PaymentMethod,
  PlanSubscription,
  PurchasePlan,
  Translation,
  UserAccount,
  UserSettings,
  UserContactUs,
  UserDashboard,
  VideoConferencing,
  UserCms,
  PaymentSuccess,
  JitsiMeet,
  PaymentSummary,
  JitsiInvitee,
  Notifications,
  OurTeam,
  Partners,
  VideoDisplay,
  ReferFriend,
  ReferralRewards,
  UnRegisterJitsiMeet,
  UnRegisterInvitee,
  AboutUs,
  DaakiaVC,
  TransparentOverlay,
  ControlBarOverlay,
  AfterSignIn,
  SolutionsWeOffer,
  HowItWorks,
  Pricing,
  Recordings,
  Whiteboard,
  Transcript,
  SaaSHostMeeting,
  SaaSParticipantMeeting,
} from "../../pages";
import userRoutesMap from "../../routeControl/userRoutes";
///
export default function route(t) {
  return [
    {
      path: userRoutesMap.HOME.path,
      name: "Home",
      key: userRoutesMap.HOME.path,
      private: false,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <Home />,
    },
    {
      path: userRoutesMap.USER_ACCOUNT.path,
      name: "User Account",
      key: userRoutesMap.USER_ACCOUNT.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <UserAccount />,
    },
    {
      path: userRoutesMap.USER_SETTINGS.path,
      name: "User Settings",
      key: userRoutesMap.USER_SETTINGS.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <UserSettings />,
    },
    {
      path: userRoutesMap.USER_DASHBOARD.path,
      name: "Dashboard",
      key: userRoutesMap.USER_DASHBOARD.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <UserDashboard />,
    },
    {
      path: userRoutesMap.PAYMENT_SUCCESS.path,
      name: "Payment success",
      key: userRoutesMap.PAYMENT_SUCCESS.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <PaymentSuccess />,
    },
    {
      path: userRoutesMap.VIDEO_CONFERENCING.path,
      name: "Video Conferencing",
      key: userRoutesMap.VIDEO_CONFERENCING.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <VideoConferencing />,
    },
    {
      path: userRoutesMap.PLAN_SUBSCRIPTION.path,
      name: "Plan Subscription",
      key: userRoutesMap.PLAN_SUBSCRIPTION.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <PlanSubscription />,
    },
    {
      path: userRoutesMap.PURCHASE_PLAN.path,
      name: "Purchase Plan",
      key: userRoutesMap.PURCHASE_PLAN.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <PurchasePlan />,
    },
    {
      path: userRoutesMap.PAYMENT_METHOD.path,
      name: "Payment Method",
      key: userRoutesMap.PAYMENT_METHOD.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <PaymentMethod />,
    },
    {
      path: userRoutesMap.USER_CONTACT_US.path,
      name: "Contact Us",
      key: userRoutesMap.USER_CONTACT_US.path,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <GoogleReCaptchaProvider reCaptchaKey={process.env.REACT_APP_GOOGLE_CAPTCHA_KEY}>
        <UserContactUs />
      </GoogleReCaptchaProvider>,
    },
    {
      path: userRoutesMap.OUR_TEAM.path,
      name: "Our Team",
      key: userRoutesMap.OUR_TEAM.path,
      private: false,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <OurTeam />,
    },
    {
      path: userRoutesMap.PARTNERS.path,
      name: "Partners",
      key: userRoutesMap.PARTNERS.path,
      private: false,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <Partners />,
    },
    {
      path: userRoutesMap.TRANSLATION.path,
      name: "Translation",
      key: userRoutesMap.TRANSLATION.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <Translation />,
    },
    {
      path: userRoutesMap.PRIVACY_POLICY.path,
      name: t("text.privacyPolicy.pageTitle"),
      key: userRoutesMap.PRIVACY_POLICY.path,
      common: true,
      private: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <UserCms />,
    },
    {
      path: userRoutesMap.END_USER_AGREEMENT.path,
      name: t("text.termsAndCondition.pageTitle"),
      key: userRoutesMap.END_USER_AGREEMENT.path,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <UserCms />,
    },
    {
      path: userRoutesMap.FAQS.path,
      name: t("text.faqs.pageTitle"),
      key: userRoutesMap.FAQS.path,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <UserCms />,
    },
    {
      path: userRoutesMap.COOKIES_POLICY.path,
      name: t("text.cookiesPolicy.pageTitle"),
      key: userRoutesMap.COOKIES_POLICY.path,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <UserCms />,
    },
    {
      path: `${userRoutesMap.JITSI_MEET.path}/:id`,
      name: "Jitsi Meet",
      key: `${userRoutesMap.JITSI_MEET.path}/:id`,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <JitsiMeet />,
    },
    {
      path: userRoutesMap.PAYMENT_SUMMARY.path,
      name: "Payment Summary",
      key: userRoutesMap.PAYMENT_SUMMARY.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <PaymentSummary />,
    },
    {
      path: `${userRoutesMap.JITSI_INVITEE.path}/:id`,
      name: "Jitsi Invitee",
      key: `${userRoutesMap.JITSI_INVITEE.path}/:id`,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <JitsiInvitee />,
    },
    {
      path: userRoutesMap.NOTIFICATION.path,
      name: "Notification",
      key: userRoutesMap.NOTIFICATION.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <Notifications />,
    },
    {
      path: `${userRoutesMap.VIDEO_DISPLAY.path}/:room_name/share/:id`,
      name: "Video",
      key: `${userRoutesMap.VIDEO_DISPLAY.path}/:room_name/share/:id`,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <VideoDisplay />,
    },
    {
      path: userRoutesMap.REFER_FRIEND.path,
      name: "Refer a friend",
      key: userRoutesMap.REFER_FRIEND.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <ReferFriend />,
    },
    {
      path: userRoutesMap.REFERRAL_REWARDS.path,
      name: "Referral & Rewards Dashboard",
      key: userRoutesMap.REFERRAL_REWARDS.path,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <ReferralRewards />,
    },
    {
      path: `${userRoutesMap.UNREGISTER_JITSI_MEET.path}/:id`,
      name: "Jitsi Meet",
      key: `${userRoutesMap.UNREGISTER_JITSI_MEET.path}/:id`,
      private: false,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <UnRegisterJitsiMeet />,
    },
    {
      path: `${userRoutesMap.UNREGISTER_INVITEE.path}/:id`,
      name: "Jitsi Invitee",
      key: `${userRoutesMap.UNREGISTER_INVITEE.path}/:id`,
      private: false,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <UnRegisterInvitee />,
    },
    {
      path: userRoutesMap.ABOUT_US.path,
      name: "About Us",
      key: userRoutesMap.ABOUT_US.path,
      private: false,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <AboutUs />,
    },
    {
      path: `${userRoutesMap.DAAKIA_VC_MEET.path}/:id`,
      name: "Daakia Meet",
      key: `${userRoutesMap.DAAKIA_VC_MEET.path}/:id`,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <DaakiaVC />,
    },
    {
      path: `${userRoutesMap.DAAKIA_VC_INVITEE.path}/:id`,
      name: "Daakia Meet",
      key: `${userRoutesMap.DAAKIA_VC_INVITEE.path}/:id`,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <DaakiaVC />,
    },
    {
      path: `${userRoutesMap.TRANSPARENT_OVERLAY.path}`,
      name: "Screenshare overlay",
      key: `${userRoutesMap.TRANSPARENT_OVERLAY.path}`,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <TransparentOverlay />,
    },
    {
      path: `${userRoutesMap.CONTROLBAR_OVERLAY.path}`,
      name:"Control bar overlay",
      key: `${userRoutesMap.CONTROLBAR_OVERLAY.path}`,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <ControlBarOverlay />,
    },
    {
      path: userRoutesMap.HOME_ELECTRON.path,
      name: "Home Electron",
      key: userRoutesMap.HOME_ELECTRON.path,
      private: false,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <HomeElectron />,
    },
    {
      path: `${userRoutesMap.ELECTRON_LOGIN.path}`,
      name: "Electron Login",
      key: `${userRoutesMap.ELECTRON_LOGIN.path}`,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <ElectronLogin />,
    },
    {
      path: `${userRoutesMap.COMPLETE_ELECTRON_LOGIN.path}`,
      name: "Complete Electron Login",
      key: `${userRoutesMap.COMPLETE_ELECTRON_LOGIN.path}`,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <CompleteElectronLogin />,
    },
    {
      path: `${userRoutesMap.AFTER_SIGN_IN.path}`,
      name: "After Sign In",
      key: `${userRoutesMap.AFTER_SIGN_IN.path}`,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <AfterSignIn />,
    },
    {
      path: `${userRoutesMap.SOLUTIONS_WE_OFFER.path}`,
      name: "Solutions We Offer",
      key: `${userRoutesMap.SOLUTIONS_WE_OFFER.path}`,
      private: false,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <SolutionsWeOffer />,
    },
    {
      path: `${userRoutesMap.HOW_IT_WORKS.path}`,
      name: "How It Works",
      key: `${userRoutesMap.HOW_IT_WORKS.path}`,
      private: false,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <HowItWorks />,
    },
    {
      path: `${userRoutesMap.PRICING.path}`,
      name: "Pricing",
      key: `${userRoutesMap.PRICING.path}`,
      private: false,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <Pricing />,
    },
    {
      path: `${userRoutesMap.SHOW_MEETING_RECORDINGS.path}/:meetingId`,
      name: "Recordings",
      key: `${userRoutesMap.SHOW_MEETING_RECORDINGS.path}/:meetingId`,
      private: true,
      common: false,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <Recordings />,
    },
    {
      path: `${userRoutesMap.WHITEBOARD.path}/:meetingId`,
      name: "Whiteboard",
      key: `${userRoutesMap.WHITEBOARD.path}/:meetingId`,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <Whiteboard />,
    },
    {
      path: `${userRoutesMap.SHOW_TRANSCRIPT.path}/:meetingId`,
      name: "Transcript",
      key: `${userRoutesMap.SHOW_TRANSCRIPT.path}/:meetingId`,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <Transcript />,
    },
    {
      path: `${userRoutesMap.SAASSHARE_HOST_MEETING.path}/:meetingId`,
      name: "SaaS Host Meeting",
      key: `${userRoutesMap.SAASSHARE_HOST_MEETING.path}/:meetingId`,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <SaaSHostMeeting />,
    },
    {
      path: `${userRoutesMap.SAASSHARE_PARTICIPANT_MEETING.path}/:meetingId`,
      name: "SaaS Participant Meeting",
      key: `${userRoutesMap.SAASSHARE_PARTICIPANT_MEETING.path}/:meetingId`,
      private: false,
      common: true,
      belongsToHeader: false,
      belongsToFooter: false,
      element: <SaaSParticipantMeeting />,
    },
  ];
}
